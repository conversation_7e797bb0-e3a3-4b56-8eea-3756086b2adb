// GPS Mock Service für Testing
export class GPSMock {
  constructor() {
    this.mockEnabled = false;
    this.currentPosition = null;
    this.route = [];
    this.routeIndex = 0;
  }

  enableMock(startLat, startLng) {
    this.mockEnabled = true;
    this.currentPosition = {lat: startLat, lng: startLng};
    console.log(`GPS Mock enabled at ${startLat}, ${startLng}`);
  }

  disableMock() {
    this.mockEnabled = false;
    this.currentPosition = null;
    console.log('GPS Mock disabled');
  }

  // Simuliere Bewegung
  moveTo(lat, lng) {
    if (!this.mockEnabled) return;
    this.currentPosition = {lat, lng};
    console.log(`GPS Mock moved to ${lat}, ${lng}`);
    
    // Trigger GPS event
    this.triggerPositionUpdate();
  }

  // Simuliere Route
  setRoute(positions) {
    this.route = positions;
    this.routeIndex = 0;
  }

  nextRoutePoint() {
    if (this.route.length === 0) return;
    
    const pos = this.route[this.routeIndex];
    this.moveTo(pos.lat, pos.lng);
    
    this.routeIndex = (this.routeIndex + 1) % this.route.length;
  }

  triggerPositionUpdate() {
    if (!this.mockEnabled || !this.currentPosition) return;
    
    // Simuliere Geolocation Event
    const mockEvent = {
      coords: {
        latitude: this.currentPosition.lat,
        longitude: this.currentPosition.lng,
        accuracy: 10
      },
      timestamp: Date.now()
    };
    
    // Trigger das gleiche Event wie echtes GPS
    window.dispatchEvent(new CustomEvent('mockGPSUpdate', {detail: mockEvent}));
  }
}

export const gpsMock = new GPSMock();