// GPS Mock Service für Testing
export class GPSMock {
  constructor() {
    this.mockEnabled = false;
    this.mapClickEnabled = false;
    this.currentPosition = null;
    this.route = [];
    this.routeIndex = 0;
    this.mapClickHandler = null;
    this.mockIndicator = null;
  }

  enableMock(startLat, startLng) {
    this.mockEnabled = true;
    this.currentPosition = {lat: startLat, lng: startLng};
    console.log(`GPS Mock enabled at ${startLat}, ${startLng}`);
    this.showMockIndicator();
  }

  disableMock() {
    this.mockEnabled = false;
    this.mapClickEnabled = false;
    this.currentPosition = null;
    this.removeMockIndicator();
    this.disableMapClickMode();
    console.log('GPS Mock disabled');
  }

  // Aktiviere Map-Click-Modus für Browser-Testing
  enableMapClickMode() {
    if (!this.mockEnabled) {
      console.warn('GPS Mock muss zuerst aktiviert werden. Verwende: testGPS.enableMock(lat, lng)');
      return;
    }

    const map = window.gameState?.map;
    if (!map) {
      console.error('Karte nicht verfügbar. Stelle sicher, dass die App vollständig geladen ist.');
      return;
    }

    this.mapClickEnabled = true;

    // Entferne vorherigen Handler falls vorhanden
    if (this.mapClickHandler) {
      map.off('click', this.mapClickHandler);
    }

    // Füge Click-Handler hinzu
    this.mapClickHandler = (e) => {
      if (this.mapClickEnabled) {
        const lat = e.latlng.lat;
        const lng = e.latlng.lng;
        this.moveTo(lat, lng);
        console.log(`🎯 GPS Mock: Bewegt zu ${lat.toFixed(6)}, ${lng.toFixed(6)} (Mausklick)`);
      }
    };

    map.on('click', this.mapClickHandler);
    console.log('🖱️ Map-Click-Modus aktiviert! Klicke auf die Karte, um dich zu bewegen.');
    this.updateMockIndicator();
  }

  // Deaktiviere Map-Click-Modus
  disableMapClickMode() {
    const map = window.gameState?.map;
    if (map && this.mapClickHandler) {
      map.off('click', this.mapClickHandler);
    }

    this.mapClickEnabled = false;
    this.mapClickHandler = null;
    console.log('🖱️ Map-Click-Modus deaktiviert');
    this.updateMockIndicator();
  }

  // Simuliere Bewegung
  moveTo(lat, lng) {
    if (!this.mockEnabled) return;
    this.currentPosition = {lat, lng};

    // Trigger GPS event
    this.triggerPositionUpdate();
  }

  // Simuliere Route
  setRoute(positions) {
    this.route = positions;
    this.routeIndex = 0;
  }

  nextRoutePoint() {
    if (this.route.length === 0) return;

    const pos = this.route[this.routeIndex];
    this.moveTo(pos.lat, pos.lng);

    this.routeIndex = (this.routeIndex + 1) % this.route.length;
  }

  triggerPositionUpdate() {
    if (!this.mockEnabled || !this.currentPosition) return;

    // Simuliere Geolocation Event
    const mockEvent = {
      coords: {
        latitude: this.currentPosition.lat,
        longitude: this.currentPosition.lng,
        accuracy: 10
      },
      timestamp: Date.now()
    };

    // Trigger das gleiche Event wie echtes GPS
    window.dispatchEvent(new CustomEvent('mockGPSUpdate', {detail: mockEvent}));
  }

  // Zeige visuellen Indikator für Mock-Modus
  showMockIndicator() {
    if (this.mockIndicator) return;

    this.mockIndicator = document.createElement('div');
    this.mockIndicator.id = 'gps-mock-indicator';
    this.mockIndicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(255, 152, 0, 0.9);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      z-index: 10000;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    `;
    this.updateMockIndicator();
    document.body.appendChild(this.mockIndicator);
  }

  // Aktualisiere Mock-Indikator Text
  updateMockIndicator() {
    if (!this.mockIndicator) return;

    const clickText = this.mapClickEnabled ? ' | 🖱️ Klick-Modus' : '';
    this.mockIndicator.textContent = `🧪 GPS Mock aktiv${clickText}`;
  }

  // Entferne Mock-Indikator
  removeMockIndicator() {
    if (this.mockIndicator) {
      this.mockIndicator.remove();
      this.mockIndicator = null;
    }
  }
}

export const gpsMock = new GPSMock();