import { gameState } from '../state/game-state.js';
import { PokemonSpawner } from '../services/pokemon-spawner.js';
import { saveSpawnsToStorage, loadSpawnsFromStorage } from '../capacitor/time-events.js';
import { config } from '../config.js';
import Pokemon from '../Pokemon.js';

// Helper function for distance calculation
function distanceMeters(lat1, lng1, lat2, lng2) {
  const R = 6371000;
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

// Initialize game state for testing
async function initTestGameState() {
  // Reset game state
  gameState.pokemons = [];
  gameState.lastSpawnLatLng = null;
  gameState.loadedFromStorage = false;
  gameState.gpsStabilizationTimer = null;
  
  // Ensure pokedex is loaded
  if (!gameState.pokedexData || Object.keys(gameState.pokedexData).length === 0) {
    console.log('Loading pokedex data...');
    try {
      const response = await fetch('./data/pokedex.json');
      gameState.pokedexData = await response.json();
      console.log(`Loaded ${Object.keys(gameState.pokedexData).length} Pokemon from pokedex`);
    } catch (error) {
      console.error('Failed to load pokedex:', error);
      throw error;
    }
  }
}

export async function testAppRestartSpawnBehavior() {
  console.log('=== APP RESTART SPAWN TEST ===');
  
  try {
    // 1. Initialize test environment
    console.log('1. Initializing test environment...');
    await initTestGameState();
    
    const spawner = new PokemonSpawner();
    const testLat = 51.96, testLng = 7.62;
    
    // 2. Initial spawn (simulate first app start)
    console.log('2. Initial spawn simulation...');
    const [initialRandom, initialLanduse] = await Promise.all([
      spawner.spawnRandomPokemons(testLat, testLng, 10),
      spawner.spawnLanduseSpecialPokemons(testLat, testLng, 10)
    ]);
    
    const initialCount = gameState.pokemons.length;
    console.log(`✓ Initial spawn: ${initialRandom.length} random + ${initialLanduse.length} landuse = ${initialCount} total`);
    
    // Set spawn location (this is what main.js does)
    gameState.lastSpawnLatLng = { lat: testLat, lng: testLng };
    console.log(`✓ Set lastSpawnLatLng to: ${testLat}, ${testLng}`);
    
    // 3. Save Pokemon data (simulate app closing)
    console.log('3. Simulating app close - saving Pokemon...');
    const pokemonDataBeforeClose = gameState.pokemons.map(p => p.toJSON());
    console.log(`✓ Saved ${pokemonDataBeforeClose.length} Pokemon to storage`);
    
    // 4. Simulate app restart - clear everything
    console.log('4. Simulating app restart - clearing state...');
    gameState.pokemons = [];
    gameState.lastSpawnLatLng = null;
    gameState.loadedFromStorage = false;
    console.log('✓ Cleared all game state');
    
    // 5. Simulate storage load (what happens in main.js time-events)
    console.log('5. Simulating storage load...');
    
    // Recreate Pokemon from storage data
    const recreatedPokemons = pokemonDataBeforeClose.map(data => Pokemon.fromJSON(data));
    gameState.pokemons = recreatedPokemons;
    gameState.loadedFromStorage = true;
    
    // Set lastSpawnLatLng based on first Pokemon (this is critical!)
    if (recreatedPokemons.length > 0) {
      gameState.lastSpawnLatLng = {
        lat: recreatedPokemons[0].spawnLat,
        lng: recreatedPokemons[0].spawnLng
      };
      console.log(`✓ Set lastSpawnLatLng from first Pokemon: ${gameState.lastSpawnLatLng.lat}, ${gameState.lastSpawnLatLng.lng}`);
    } else {
      console.log('❌ No Pokemon to restore lastSpawnLatLng from!');
    }
    
    const afterRestartCount = gameState.pokemons.length;
    console.log(`✓ After restart: ${afterRestartCount} Pokemon loaded, loadedFromStorage=${gameState.loadedFromStorage}`);
    
    // 6. Simulate GPS position update (the critical test)
    console.log('6. Simulating GPS position update at same location...');
    const gpsLat = testLat, gpsLng = testLng;
    
    console.log(`GPS coordinates: ${gpsLat}, ${gpsLng}`);
    console.log(`lastSpawnLatLng: ${gameState.lastSpawnLatLng?.lat}, ${gameState.lastSpawnLatLng?.lng}`);
    console.log(`loadedFromStorage: ${gameState.loadedFromStorage}`);
    
    // Check what main.js logic would do
    let shouldSpawn = false;
    let spawnReason = '';
    
    if (!gameState.lastSpawnLatLng) {
      if (!gameState.loadedFromStorage) {
        shouldSpawn = true;
        spawnReason = 'No lastSpawnLatLng and not loaded from storage';
      } else {
        shouldSpawn = false;
        spawnReason = 'No lastSpawnLatLng but loaded from storage - BUG SCENARIO!';
        console.log('❌ CRITICAL BUG: lastSpawnLatLng is null but loadedFromStorage is true!');
      }
    } else {
      const dist = distanceMeters(gpsLat, gpsLng, gameState.lastSpawnLatLng.lat, gameState.lastSpawnLatLng.lng);
      console.log(`Distance from last spawn: ${dist.toFixed(1)}m (trigger: ${config.pokemon.spawnDistanceTrigger}m)`);
      
      if (dist >= config.pokemon.spawnDistanceTrigger) {
        shouldSpawn = true;
        spawnReason = `Distance ${dist.toFixed(1)}m exceeds trigger ${config.pokemon.spawnDistanceTrigger}m`;
      } else {
        shouldSpawn = false;
        spawnReason = `Distance ${dist.toFixed(1)}m within trigger threshold`;
      }
    }
    
    console.log(`Spawn decision: ${shouldSpawn ? 'WILL SPAWN' : 'NO SPAWN'} - ${spawnReason}`);
    
    // 7. Test actual spawn behavior
    console.log('7. Testing actual spawn behavior...');
    const beforeSpawnCount = gameState.pokemons.length;
    
    if (shouldSpawn) {
      console.log('Executing spawn...');
      const [newRandom, newLanduse] = await Promise.all([
        spawner.spawnRandomPokemons(gpsLat, gpsLng, 10),
        spawner.spawnLanduseSpecialPokemons(gpsLat, gpsLng, 10)
      ]);
      console.log(`New spawn: ${newRandom.length} random + ${newLanduse.length} landuse`);
    } else {
      console.log('No spawn executed (correct behavior)');
    }
    
    const afterSpawnCount = gameState.pokemons.length;
    const newSpawned = afterSpawnCount - beforeSpawnCount;
    
    console.log(`\n=== FINAL RESULTS ===`);
    console.log(`Initial spawn: ${initialCount} Pokemon`);
    console.log(`After restart: ${afterRestartCount} Pokemon`);
    console.log(`New spawns: ${newSpawned} Pokemon`);
    console.log(`Final total: ${afterSpawnCount} Pokemon`);
    
    const bugDetected = newSpawned > 0 && !shouldSpawn;
    
    if (bugDetected) {
      console.error('❌ BUG DETECTED: Unexpected spawns after app restart!');
    } else if (newSpawned === 0) {
      console.log('✅ CORRECT: No duplicate spawns after restart');
    } else {
      console.log('✅ CORRECT: Spawns occurred as expected');
    }
    
    return {
      initialCount,
      afterRestartCount,
      newSpawned,
      bugDetected,
      spawnReason
    };
    
  } catch (error) {
    console.error('Test failed:', error);
    throw error;
  }
}
