// main.js
// Main entry point for the GPS Pokemon App

import { config } from './config.js';
import { logger } from './utils/logger.js';
import { gameState } from './state/game-state.js';
import { MapRenderer } from './services/map-renderer.js';
import { playerRenderer } from './services/player-renderer.js';
import { PokemonSpawner, distanceMeters, calculateAzimuth } from './services/pokemon-spawner.js';
import { fabManager } from './ui/FabManager.js';
import { storageService } from './storage/storage-service.js';
import { addEncounter } from './storage/encountersStorage.js';
import { startWatchPosition, requestLocationPermissions } from './capacitor/geolocation.js';
import { keepScreenAwake } from './capacitor/keep-awake.js';
import { registerMainMapBackButtonHandler } from './capacitor/app.js';
import { pokemonManager } from './services/pokemon-manager.js';
import {
  initializeTimeEvents,
  setupHourChangeTimer,
  removePokemonFromStorage,
  syncGameStateWithStorage,
  clearSpawnsFromStorage
} from './capacitor/time-events.js';
import { trainerSpawner } from './services/trainer-spawner.js';
import { gpsMock } from './services/gps-mock.js';

// Globaler Fehlerhandler für unbehandelte Fehler
window.onerror = function(message, file, line, col, error) {
  logger.error(`Unhandled error: ${message} at ${file}:${line}:${col}`, error);
  return false; // false bedeutet, dass der Fehler nicht behandelt wurde
};

// Globaler Fehlerhandler für unbehandelte Promise-Fehler
window.addEventListener('unhandledrejection', function(event) {
  logger.error('Unhandled promise rejection:', event.reason);
});

// Initialize the app
async function initApp() {
  try {
    logger.info('Initializing app...');

    // Initialize game state
    await gameState.initialize();

    // Initialize Pokemon manager
    await pokemonManager.initialize();
    logger.info('Pokemon manager initialized');

    // Initialize map
    const mapRenderer = new MapRenderer();
    const map = mapRenderer.initMap();

    // Make mapRenderer globally available for debug features
    window.mapRenderer = mapRenderer;

    // Add CSS for player sprite
    const playerCssLink = document.createElement('link');
    playerCssLink.rel = 'stylesheet';
    playerCssLink.href = './styles/player.css';
    document.head.appendChild(playerCssLink);

    // Initialize player sprite with default position
    // We'll use the map's center as the initial position until we get a GPS fix
    if (config.ui.showPlayerSprite) {
      const { defaultCenter } = config.map;
      // Initialize player sprite with static state (not moving)
      playerRenderer.initPlayerSprite(defaultCenter[0], defaultCenter[1], 'm');

      // Log to check if the sprite was created
      logger.debug(`Player sprite initialized at map center: ${defaultCenter}`);

      // Force update of sprites to ensure they're visible
      playerRenderer.updateDirection('n');
      playerRenderer.setMoving(false);
    }

    // Initialize Pokemon spawner
    const pokemonSpawner = new PokemonSpawner();

    // Initialize time-event system
    if (config.timeEvents.enabled) {
      logger.info('Initializing time-event system...');
      const timeEventResult = await initializeTimeEvents();

      // Add a flag to gameState to track if Pokemon were loaded from storage
      gameState.loadedFromStorage = false;

      if (!timeEventResult.isNewSlot && timeEventResult.spawns.length > 0) {
        // We're in the same time slot, load spawns from storage
        logger.info(`Loading ${timeEventResult.spawns.length} Pokemon spawns from storage`);

        // Clear any existing Pokemon first to avoid duplicates
        if (gameState.pokemons.length > 0) {
          logger.debug(`Clearing ${gameState.pokemons.length} existing Pokemon before loading from storage`);
          gameState.pokemons = [];
        }

        // Ensure pokedex data is loaded before recreating Pokemon
        if (!gameState.pokedexData || gameState.pokedexData.length === 0) {
          try {
            logger.info('Loading pokedex data before recreating Pokemon from storage');
            await gameState.loadPokedexData();
            logger.info(`Loaded pokedex data with ${gameState.pokedexData.length} Pokemon`);
          } catch (e) {
            logger.error('Error loading pokedex data:', e);
          }
        }

        // Recreate Pokemon from storage
        const recreated = pokemonSpawner.recreateStoredPokemons(timeEventResult.spawns);

        // Set the flag to indicate Pokemon were loaded from storage
        gameState.loadedFromStorage = true;

        // Check if we have the player's current position
        if (navigator.geolocation && recreated.length > 0) {
          // Get the current position to check if player has moved far from stored Pokemon
          navigator.geolocation.getCurrentPosition(
            (position) => {
              const currentLat = position.coords.latitude;
              const currentLng = position.coords.longitude;

              // Calculate average position of stored Pokemon
              let avgLat = 0;
              let avgLng = 0;
              recreated.forEach(pokemon => {
                avgLat += pokemon.spawnLat;
                avgLng += pokemon.spawnLng;
              });
              avgLat /= recreated.length;
              avgLng /= recreated.length;

              // Check distance from current position to average Pokemon position
              const distToStoredPokemon = distanceMeters(currentLat, currentLng, avgLat, avgLng);

              if (distToStoredPokemon > config.pokemon.spawnRadius) {
                // Player is far from stored Pokemon, clear them and allow new spawns
                logger.info(`Player is ${distToStoredPokemon}m from stored Pokemon (> ${config.pokemon.spawnRadius}m) - will generate new spawns`);
                gameState.pokemons = [];
                gameState.loadedFromStorage = false;
                gameState.lastSpawnLatLng = null; // Force new spawns

                // Clear storage since these Pokemon are no longer relevant
                if (config.timeEvents.enabled) {
                  clearSpawnsFromStorage().catch(e => {
                    logger.error('Error clearing spawns from storage:', e);
                  });
                }
              } else {
                // Player is still near stored Pokemon, keep them
                logger.info(`Player is ${distToStoredPokemon}m from stored Pokemon (< ${config.pokemon.spawnRadius}m) - keeping stored spawns`);
                // Use the average position as the last spawn point
                gameState.lastSpawnLatLng = { lat: avgLat, lng: avgLng };
                logger.debug(`Set lastSpawnLatLng to ${avgLat}, ${avgLng} based on average position of stored Pokemon`);
              }
            },
            (error) => {
              // If we can't get the current position, just use the first Pokemon's position
              logger.warn(`Could not get current position: ${error.message} - using first Pokemon's position`);
              const firstPokemon = recreated[0];

              // Set lastSpawnLatLng to the first Pokemon's position
              gameState.lastSpawnLatLng = {
                lat: firstPokemon.spawnLat,
                lng: firstPokemon.spawnLng
              };

              // Set a flag to indicate that we should use a more conservative distance check
              // on the first GPS position to avoid unnecessary spawns
              gameState.gpsTimeoutOccurred = true;
              logger.debug('Setting gpsTimeoutOccurred flag to prevent unnecessary spawns on first GPS position');
            },
            { timeout: config.geolocation.timeout, maximumAge: config.geolocation.maximumAge }
          );
        } else if (recreated.length > 0) {
          // Fallback if geolocation is not available
          const firstPokemon = recreated[0];
          gameState.lastSpawnLatLng = {
            lat: firstPokemon.spawnLat,
            lng: firstPokemon.spawnLng
          };
          logger.debug(`Geolocation not available - set lastSpawnLatLng to ${gameState.lastSpawnLatLng.lat}, ${gameState.lastSpawnLatLng.lng} based on first Pokemon`);
        }

        // Render the Pokemon on the map
        mapRenderer.renderPokemons(true);
      } else {
        logger.info('New time slot or no stored spawns, will generate new spawns');
      }

      // We call this for backward compatibility, but it doesn't do anything anymore
      setupHourChangeTimer();

      // We don't need to listen for time slot changes anymore since they only happen on app restart
      // But we keep the code for backward compatibility
      gameState.events.on('timeSlotChanged', () => {
        logger.info('Time slot changed event received (should not happen during app session)');
      });

      // Listen for Pokemon updates to keep storage in sync
      gameState.events.on('pokemonsUpdated', async () => {
        if (config.timeEvents.enabled && !gameState.disableStorageSync) {
          try {
            await syncGameStateWithStorage();
            logger.debug(`Synced ${gameState.pokemons.length} Pokemon with storage after update`);
          } catch (e) {
            logger.error('Error syncing game state with storage after Pokemon update:', e);
          }
        } else if (gameState.disableStorageSync) {
          logger.debug('Storage sync disabled - skipping sync after Pokemon update');
        }
      });
    }

    // Initialize FAB manager
    fabManager.initialize();

    // Setup map pan listener
    setTimeout(() => mapRenderer.setupMapPanListener(), 1000);

    // Request location permissions if needed
    await requestLocationPermissions();

    // Keep the screen awake while the app is running
    const awakeSuccess = await keepScreenAwake();
    if (!awakeSuccess) {
      logger.warn('Failed to keep screen awake. The screen may turn off after a period of inactivity.');
    }

    // Start watching user location
    startLocationWatch(mapRenderer, pokemonSpawner);

    // Start Pokemon movement animation
    startPokemonMovement();

    // Setup device orientation listener
    setupDeviceOrientation();

    // Setup hardware back button handler for the main map
    setupBackButtonHandler();

    logger.info('App initialized successfully');
  } catch (e) {
    logger.error('Error initializing app:', e);
    alert('Error initializing app: ' + e.message);
  }
}

/**
 * Start watching the user's location
 * @param {MapRenderer} mapRenderer - The map renderer
 * @param {PokemonSpawner} pokemonSpawner - The Pokemon spawner
 */
function startLocationWatch(mapRenderer, pokemonSpawner) {
  let stopGpsWatch = null;

  if (typeof stopGpsWatch === 'function') {
    stopGpsWatch();
  }

  stopGpsWatch = startWatchPosition((pos, err) => {
    if (err) {
      logger.error('Geolocation error:', err);
      if (err.code === 1) {
        alert('GPS access denied. Please allow access to your location.');
      } else if (err.code === 2) {
        alert('Location unavailable. Please try again later.');
      } else if (err.code === 3) {
        alert('Location determination timed out.');
      }
      return;
    }

    const latitude = pos.coords.latitude;
    const longitude = pos.coords.longitude;

    onLocationFound({
      latlng: { lat: latitude, lng: longitude },
      coords: pos.coords
    }, mapRenderer, pokemonSpawner);
  });
}

/**
 * Handle location found event
 * @param {Object} e - The location event
 * @param {MapRenderer} mapRenderer - The map renderer
 * @param {PokemonSpawner} pokemonSpawner - The Pokemon spawner
 */
async function onLocationFound(e, mapRenderer, pokemonSpawner) {
  // Extract lat/lng robustly
  const lat = e?.latlng?.lat ?? e?.latitude ?? e?.coords?.latitude;
  const lng = e?.latlng?.lng ?? e?.longitude ?? e?.coords?.longitude;

  if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) {
    alert('Could not read location data.');
    return;
  }

  // Update game state
  gameState.lastUserLatLng = { lat, lng };

  // Check if we need to spawn Pokemon
  if (!gameState.lastSpawnLatLng) {
    // First spawn - but only if we didn't load from storage
    if (!gameState.loadedFromStorage) {
      // Add GPS stabilization delay to prevent double spawning
      if (!gameState.gpsStabilizationTimer) {
        logger.debug('GPS position received - starting stabilization timer (3 seconds)');
        gameState.gpsStabilizationTimer = setTimeout(async () => {
          logger.debug('GPS stabilization complete - generating initial Pokemon');
          try {
            // Pre-load landuse cache once to avoid race conditions between spawn methods
            logger.debug('[CACHE_DEBUG] Pre-loading landuse cache for initial spawn...');
            await pokemonSpawner.loadLanduseDataForArea(lat, lng, pokemonSpawner.spawnRadius);
            logger.debug(`[CACHE_DEBUG] Pre-loaded cache with ${pokemonSpawner.landuseCache?.data?.features?.length || 0} features`);

            // Spawn both types of Pokemon in parallel and wait for both to complete
            logger.info(`[MAIN_DEBUG] 🚀 About to spawn Pokemon: ${config.pokemon.spawnBatchSize} standard + ${config.pokemon.landuseSpecialBatchSize} landuse`);

            let standardSpawns = [];
            let landuseSpawns = [];

            try {
              logger.info(`[MAIN_DEBUG] 📍 Calling spawnRandomPokemons...`);
              standardSpawns = await pokemonSpawner.spawnRandomPokemons(lat, lng, config.pokemon.spawnBatchSize);
              logger.info(`[MAIN_DEBUG] ✅ spawnRandomPokemons completed: ${standardSpawns.length} Pokemon`);
            } catch (error) {
              logger.error(`[MAIN_DEBUG] ❌ Error in spawnRandomPokemons:`, error);
            }

            try {
              logger.info(`[MAIN_DEBUG] 🏞️ Calling spawnLanduseSpecialPokemons...`);
              landuseSpawns = await pokemonSpawner.spawnLanduseSpecialPokemons(lat, lng, config.pokemon.landuseSpecialBatchSize);
              logger.info(`[MAIN_DEBUG] ✅ spawnLanduseSpecialPokemons completed: ${landuseSpawns.length} Pokemon`);
            } catch (error) {
              logger.error(`[MAIN_DEBUG] ❌ Error in spawnLanduseSpecialPokemons:`, error);
            }

            logger.info(`[MAIN_DEBUG] 📊 Final result: ${standardSpawns.length} standard + ${landuseSpawns.length} landuse = ${standardSpawns.length + landuseSpawns.length} total Pokemon`);
            mapRenderer.renderPokemons(true);
          } catch (error) {
            logger.error('Error during initial Pokemon spawning:', error);
            // Fallback: just render any Pokemon that were created
            mapRenderer.renderPokemons(true);
          }
          gameState.lastSpawnLatLng = { lat, lng };
          gameState.gpsStabilizationTimer = null;
        }, 3000); // 3 second delay for GPS stabilization
        return;
      } else {
        // Timer is already running, just update the position for when it fires
        logger.debug('GPS stabilization timer already running - updating target position');
        return;
      }
    } else {
      // We loaded from storage but somehow lastSpawnLatLng is not set
      // This shouldn't happen, but just in case
      logger.debug('Pokemon loaded from storage but lastSpawnLatLng not set - setting it now');
      gameState.lastSpawnLatLng = { lat, lng };
      mapRenderer.renderPokemons(); // Just update markers
    }
  } else {
    const dist = distanceMeters(lat, lng, gameState.lastSpawnLatLng.lat, gameState.lastSpawnLatLng.lng);

    // Check if this is the first GPS position after a timeout
    if (gameState.gpsTimeoutOccurred) {
      // If we had a GPS timeout, be more conservative with the first position
      // Only spawn new Pokemon if the player is really far away (2x the normal trigger distance)
      if (dist >= config.pokemon.spawnDistanceTrigger * 2) {
        logger.debug(`Player is ${dist}m from last spawn point after GPS timeout - this exceeds 2x trigger distance, generating new Pokemon`);
        try {
          // Pre-load landuse cache once to avoid race conditions between spawn methods
          logger.debug('[CACHE_DEBUG] Pre-loading landuse cache for GPS timeout spawn...');
          await pokemonSpawner.loadLanduseDataForArea(lat, lng, pokemonSpawner.spawnRadius);
          logger.debug(`[CACHE_DEBUG] Pre-loaded cache with ${pokemonSpawner.landuseCache?.data?.features?.length || 0} features`);

          // Spawn both types of Pokemon in parallel and wait for both to complete
          const [standardSpawns, landuseSpawns] = await Promise.all([
            pokemonSpawner.spawnRandomPokemons(lat, lng, config.pokemon.spawnBatchSize),
            pokemonSpawner.spawnLanduseSpecialPokemons(lat, lng, config.pokemon.landuseSpecialBatchSize)
          ]);
          logger.debug(`Spawned ${standardSpawns.length} standard Pokemon and ${landuseSpawns.length} landuse Pokemon after GPS timeout`);
          mapRenderer.renderPokemons(true);
        } catch (error) {
          logger.error('Error during Pokemon spawning after GPS timeout:', error);
          // Fallback: just render any Pokemon that were created
          mapRenderer.renderPokemons(true);
        }
        gameState.lastSpawnLatLng = { lat, lng };
      } else {
        logger.debug(`Player is ${dist}m from last spawn point after GPS timeout - this is within 2x trigger distance, keeping existing Pokemon`);
        mapRenderer.renderPokemons(); // Just update markers
      }
      // Clear the flag after handling the first position
      gameState.gpsTimeoutOccurred = false;
      logger.debug('Cleared gpsTimeoutOccurred flag after handling first GPS position');
    } else if (dist >= config.pokemon.spawnDistanceTrigger) {
      // Normal case - player has moved far enough to trigger new spawns
      logger.debug(`Player moved ${dist}m from last spawn point - generating new Pokemon`);
      try {
        // Pre-load landuse cache once to avoid race conditions between spawn methods
        logger.debug('[CACHE_DEBUG] Pre-loading landuse cache for movement spawn...');
        await pokemonSpawner.loadLanduseDataForArea(lat, lng, pokemonSpawner.spawnRadius);
        logger.debug(`[CACHE_DEBUG] Pre-loaded cache with ${pokemonSpawner.landuseCache?.data?.features?.length || 0} features`);

        // Spawn both types of Pokemon in parallel and wait for both to complete
        const [standardSpawns, landuseSpawns] = await Promise.all([
          pokemonSpawner.spawnRandomPokemons(lat, lng, config.pokemon.spawnBatchSize),
          pokemonSpawner.spawnLanduseSpecialPokemons(lat, lng, config.pokemon.landuseSpecialBatchSize)
        ]);
        logger.debug(`Spawned ${standardSpawns.length} standard Pokemon and ${landuseSpawns.length} landuse Pokemon after movement`);
        mapRenderer.renderPokemons(true);
      } catch (error) {
        logger.error('Error during Pokemon spawning after movement:', error);
        // Fallback: just render any Pokemon that were created
        mapRenderer.renderPokemons(true);
      }
      gameState.lastSpawnLatLng = { lat, lng };
    } else {
      mapRenderer.renderPokemons(); // Just update markers
    }
  }

  // Test: Spawne einen Trainer wenn noch keiner existiert
  if (gameState.trainers.length === 0) {
    logger.debug('Spawning test trainer...');
    try {
      const trainer = await trainerSpawner.spawnRandomTrainer(lat, lng);
      if (trainer) {
        gameState.addTrainer(trainer);
        await mapRenderer.renderTrainers(true);
      }
    } catch (error) {
      logger.error('Error spawning test trainer:', error);
    }
  }

  // Update catch radius circle
  mapRenderer.updateCatchRadiusCircle(lat, lng);

  // Compass marker logic
  let heading = 0;
  let useMovementHeading = false;
  const gpsSpeed = e?.coords?.speed;
  const now = Date.now();

  if (!gameState.lastMoveTimestamp) gameState.lastMoveTimestamp = now;
  if (!gameState.lastMovePosition) gameState.lastMovePosition = { lat, lng };

  // Variable für die Distanz seit der letzten Bewegung
  let distSinceLastMove = 0;

  // Check if position has changed significantly (>2m)
  try {
    // Sicherstellen, dass lastMovePosition gültige Werte hat
    if (!gameState.lastMovePosition ||
        typeof gameState.lastMovePosition.lat !== 'number' ||
        typeof gameState.lastMovePosition.lng !== 'number' ||
        isNaN(gameState.lastMovePosition.lat) ||
        isNaN(gameState.lastMovePosition.lng)) {
      // Wenn lastMovePosition ungültig ist, setzen wir es auf die aktuelle Position
      logger.debug('lastMovePosition is invalid, resetting to current position');
      gameState.lastMovePosition = { lat, lng };
    }

    distSinceLastMove = distanceMeters(lat, lng, gameState.lastMovePosition.lat, gameState.lastMovePosition.lng);
    if (distSinceLastMove > 2) {
      gameState.lastMoveTimestamp = now;
      gameState.lastMovePosition = { lat, lng };
    }
  } catch (e) {
    // Fehlerbehandlung für den Fall, dass etwas schief geht
    logger.error('Error calculating distance since last move:', e);
    // Setze lastMovePosition auf die aktuelle Position, um weitere Fehler zu vermeiden
    gameState.lastMovePosition = { lat, lng };
    // Setze distSinceLastMove auf 0, um weitere Fehler zu vermeiden
    distSinceLastMove = 0;
  }

  // If less than 5s since last movement: use movement direction
  // Otherwise (standing for 5s or more): use hardware compass
  if (now - gameState.lastMoveTimestamp < 5000) {
    // Moving: use movement direction
    if (typeof gpsSpeed === 'number' && gpsSpeed > 1 && gameState.lastGpsForHeading) {
      // Use heading from GPS if available
      heading = calculateAzimuth(gameState.lastGpsForHeading.lat, gameState.lastGpsForHeading.lng, lat, lng);
      useMovementHeading = true;
    } else if (gameState.lastGpsForHeading) {
      // Fallback: check if position has changed significantly
      const dist = distanceMeters(lat, lng, gameState.lastGpsForHeading.lat, gameState.lastGpsForHeading.lng);
      if (dist > 2) {
        heading = calculateAzimuth(gameState.lastGpsForHeading.lat, gameState.lastGpsForHeading.lng, lat, lng);
        useMovementHeading = true;
      }
    }

    // Store movement direction (azimuth)
    if (useMovementHeading) {
      gameState.lastMovementHeading = heading;
    }
  } else {
    // Standing for at least 5s: use hardware compass
    heading = gameState.lastKnownHeading || 0;
    useMovementHeading = false;
  }

  // Store current GPS position for next calculation
  gameState.lastGpsForHeading = { lat, lng };

  // Update compass display
  if (!useMovementHeading && typeof gameState.lastKnownHeading === 'number') {
    heading = gameState.lastKnownHeading;
  }

  mapRenderer.updateCompassMarker(lat, lng, heading);

  // Update player sprite if enabled
  if (config.ui.showPlayerSprite) {
    logger.debug(`Updating player sprite at ${lat}, ${lng}, heading: ${heading}`);

    if (!playerRenderer.playerMarker) {
      // Initialize player sprite if not already initialized
      logger.debug('Initializing player sprite for the first time');
      playerRenderer.initPlayerSprite(lat, lng, 'm'); // 'm' for male
    } else {
      // Always update the position directly first to ensure the sprite is visible
      playerRenderer.playerMarker.setLatLng([lat, lng]);

      // Check if player is moving or just rotating
      // Only consider the player moving if they've moved more than 2 meters
      const isMoving = distSinceLastMove > 2;
      logger.debug(`Player is ${isMoving ? 'moving' : 'static'}, distSinceLastMove: ${distSinceLastMove}`);

      if (isMoving) {
        // Player is moving - update position with animation
        playerRenderer.updatePosition(lat, lng);
      } else {
        // Player is just rotating - update direction only
        playerRenderer.updateDirection(heading);
      }
    }
  }

  // Automatic centering if active
  if (!gameState.hasCenteredOnUser) {
    gameState.map.setView([lat, lng], gameState.map.getZoom());
    gameState.hasCenteredOnUser = true;
    gameState.isCenteringActive = true; // Activate after first fix
  } else if (gameState.shouldRecenterOnNextLocation) {
    gameState.map.setView([lat, lng], gameState.map.getZoom());
    gameState.shouldRecenterOnNextLocation = false;
    mapRenderer.hideCenterButton();
    gameState.isCenteringActive = true; // Activate after button click
  } else if (gameState.isCenteringActive) {
    // Only center automatically if centering is active
    gameState.map.setView([lat, lng], gameState.map.getZoom());
  }
}

/**
 * Start Pokemon movement animation
 */
function startPokemonMovement() {
  if (gameState.pokemonMoveInterval) clearInterval(gameState.pokemonMoveInterval);

  gameState.pokemonMoveInterval = setInterval(() => {
    gameState.pokemons.forEach((pokemon) => {
      if (isNaN(pokemon.spawnLat) || isNaN(pokemon.spawnLng)) {
        logger.error('Invalid spawnLat/spawnLng for Pokemon:', pokemon);
        return;
      }

      // Random offset from spawn point
      const offset = {
        lat: (Math.random() - 0.5) * 0.0002, // About 20 meters
        lng: (Math.random() - 0.5) * 0.0002
      };

      const newLat = pokemon.spawnLat + offset.lat;
      const newLng = pokemon.spawnLng + offset.lng;

      animatePokemonMove(pokemon, newLat, newLng);
    });
  }, config.pokemon.moveInterval);
}

/**
 * Animate Pokemon movement
 * @param {Object} pokemon - The Pokemon to animate
 * @param {number} newLat - The new latitude
 * @param {number} newLng - The new longitude
 */
function animatePokemonMove(pokemon, newLat, newLng) {
  const steps = config.pokemon.moveAnimationSteps;
  // duration is used for calculating the animation timing
  const oldLat = pokemon.lat;
  const oldLng = pokemon.lng;
  let step = 0;

  function animate() {
    step++;
    const t = step / steps;
    pokemon.lat = oldLat + (newLat - oldLat) * t;
    pokemon.lng = oldLng + (newLng - oldLng) * t;

    const entry = gameState.pokemonMarkers.get(pokemon.id);
    if (entry && entry.marker) {
      entry.marker.setLatLng([pokemon.lat, pokemon.lng]);
    }

    if (step < steps) {
      requestAnimationFrame(animate);
    } else {
      pokemon.lat = newLat;
      pokemon.lng = newLng;

      const entry = gameState.pokemonMarkers.get(pokemon.id);
      if (entry && entry.marker) {
        entry.marker.setLatLng([pokemon.lat, pokemon.lng]);
      }
    }
  }

  animate();
}

/**
 * Setup device orientation listener
 */
function setupDeviceOrientation() {
  if (window.DeviceOrientationEvent) {
    window.addEventListener('deviceorientationabsolute' in window ? 'deviceorientationabsolute' : 'deviceorientation', function(event) {
      let heading = null;

      if (event.absolute && event.alpha !== null) {
        heading = event.alpha;
      } else if (event.webkitCompassHeading !== undefined) {
        heading = event.webkitCompassHeading;
      } else if (event.alpha !== null) {
        heading = 360 - event.alpha; // fallback
      }

      if (typeof heading === 'number' && !isNaN(heading)) {
        gameState.lastKnownHeading = heading;
      }
    }, true);
  }
}

/**
 * Setup hardware back button handler for the main map
 * Implements "Double Back to Exit" behavior
 */
function setupBackButtonHandler() {
  // Registriere den "Double Back to Exit"-Handler für die Hauptkarte
  // Dieser wird nur aktiv, wenn kein anderer Screen geöffnet ist
  registerMainMapBackButtonHandler();
}

/**
 * Catch a Pokemon by ID - adds to encounters for battle, not to caught storage
 * @param {string} id - The Pokemon ID
 */
window.catchPokemonById = async function(id) {
  if (!gameState.lastUserLatLng) return;

  const userLatLng = gameState.lastUserLatLng;
  const pokemon = gameState.pokemons.find(p => p.id === id);

  if (!pokemon) return;

  const dist = distanceMeters(userLatLng.lat, userLatLng.lng, pokemon.lat, pokemon.lng);

  if (dist <= config.pokemon.catchRadius) {
    try {
      // Add encounter - this is where the Pokemon goes for battles
      // Do NOT add to pokedex or caught storage until battle is won
      await addEncounter({ id: pokemon.id, name: pokemon.name, level: pokemon.level });
      logger.debug(`Added Pokemon ${pokemon.name} to encounters for battle`);

      // Remove from storage if time events are enabled
      if (config.timeEvents.enabled) {
        try {
          await removePokemonFromStorage(id);
          logger.debug(`Removed Pokemon ${pokemon.name} (ID: ${id}) from map storage`);
        } catch (e) {
          logger.error('Error removing Pokemon from map storage:', e);
        }
      }

      // Remove from map
      gameState.removePokemon(id);

      // Remove marker
      if (gameState.pokemonMarkers.has(id)) {
        gameState.map.removeLayer(gameState.pokemonMarkers.get(id).marker);
        gameState.pokemonMarkers.delete(id);
      }

      // Render Pokemon
      const mapRenderer = new MapRenderer();
      mapRenderer.renderPokemons(true);

      // Sync game state with storage after encounter
      if (config.timeEvents.enabled) {
        try {
          await syncGameStateWithStorage();
          logger.debug('Synced game state with storage after adding encounter');
        } catch (e) {
          logger.error('Error syncing game state with storage after encounter:', e);
        }
      }

      alert('Pokemon encountered! Go to Encounters screen to battle it.');
    } catch (e) {
      logger.error('Error encountering Pokemon:', e);
      alert('Error encountering Pokemon. Please try again.');
    }
  } else {
    alert(`Too far away! Get closer (within ${config.pokemon.catchRadius}m) to encounter this Pokémon.`);
  }
};

/**
 * Challenge a trainer by ID
 * @param {string} trainerId - The trainer ID
 */
window.challengeTrainer = async function(trainerId) {
  const trainer = gameState.trainers.find(t => t.id === trainerId);
  if (!trainer) {
    alert('Trainer nicht gefunden!');
    return;
  }

  // Prüfe Entfernung
  if (!gameState.lastUserLatLng) {
    alert('Position nicht verfügbar!');
    return;
  }

  const distance = distanceMeters(
    gameState.lastUserLatLng.lat,
    gameState.lastUserLatLng.lng,
    trainer.lat,
    trainer.lng
  );

  if (distance > 50) {
    alert(`Zu weit entfernt! Komm näher (${Math.round(distance)}m entfernt, max. 50m)`);
    return;
  }

  // Prüfe ob Spieler genau 6 Pokemon im Team hat
  try {
    await pokemonManager.initialize();
    const playerTeam = pokemonManager.getTeamPokemon();

    if (!playerTeam || playerTeam.length !== 6) {
      alert('Du brauchst genau 6 Pokémon in deinem Team für einen Trainerkampf! Gehe zum Team-Bildschirm und stelle dein Team zusammen.');
      return;
    }

    // Prüfe ob Trainer genau 6 Pokemon hat
    if (!trainer.team || trainer.team.length !== 6) {
      alert('Der Trainer hat kein vollständiges Team! Kampf kann nicht gestartet werden.');
      return;
    }

    // Starte Trainerkampf
    const { trainerBattleScreen } = await import('./ui/TrainerBattleScreen.js');
    await trainerBattleScreen.startBattle(trainer);
  } catch (e) {
    logger.error('Error starting trainer battle:', e);
    alert('Fehler beim Starten des Trainerkampfs: ' + e.message);
  }
};

// Initialize the app when the DOM is loaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initApp, 0);
} else {
  document.addEventListener('DOMContentLoaded', initApp);
}

// GPS Mock Event Listener
window.addEventListener('mockGPSUpdate', (event) => {
  const position = event.detail;
  handleLocationUpdate(position.coords.latitude, position.coords.longitude);
});

// Debug-Funktionen für Browser Console
window.testGPS = {
  enableMock: (lat, lng) => {
    gpsMock.enableMock(lat, lng);
    console.log('GPS Mock aktiviert. Verwende testGPS.enableMapClick() für Mausklick-Steuerung.');
  },
  enableMapClick: () => gpsMock.enableMapClickMode(),
  disableMapClick: () => gpsMock.disableMapClickMode(),
  disable: () => gpsMock.disableMock(),
  moveTo: (lat, lng) => gpsMock.moveTo(lat, lng),
  simulateWalk: () => {
    const route = [
      {lat: 51.96, lng: 7.62},
      {lat: 51.965, lng: 7.625},
      {lat: 51.97, lng: 7.63},
      {lat: 51.975, lng: 7.635}
    ];
    gpsMock.setRoute(route);

    let i = 0;
    const interval = setInterval(() => {
      gpsMock.nextRoutePoint();
      i++;
      if (i >= route.length) clearInterval(interval);
    }, 2000);
  },

  help: () => {
    console.log(`
🧪 GPS Mock Test-Funktionen:

Grundlegende Funktionen:
  testGPS.enableMock(lat, lng)    - GPS Mock aktivieren
  testGPS.disable()               - GPS Mock deaktivieren
  testGPS.moveTo(lat, lng)        - Zu Position bewegen

Mausklick-Steuerung (nur Browser):
  testGPS.enableMapClick()        - Mausklick-Modus aktivieren
  testGPS.disableMapClick()       - Mausklick-Modus deaktivieren

Automatische Tests:
  testGPS.simulateWalk()          - Simuliere Spaziergang
  testGPS.simulateMovement(lat, lng) - Direkte Bewegung

Beispiel:
  testGPS.enableMock(51.96, 7.62)  // GPS Mock starten
  testGPS.enableMapClick()         // Mausklicks aktivieren
  // Jetzt auf die Karte klicken zum Bewegen!
    `);
  }
};

// Debug-Funktionen für Browser Console
window.debugSpawn = {
  checkState: () => {
    console.log('=== SPAWN STATE DEBUG ===');
    console.log('Pokemon count:', gameState.pokemons.length);
    console.log('lastSpawnLatLng:', gameState.lastSpawnLatLng);
    console.log('loadedFromStorage:', gameState.loadedFromStorage);
    console.log('gpsStabilizationTimer:', gameState.gpsStabilizationTimer);
  },

  simulateMovement: (lat, lng) => {
    console.log(`Simulating movement to ${lat}, ${lng}`);
    handleLocationUpdate(lat, lng);
  }
};

